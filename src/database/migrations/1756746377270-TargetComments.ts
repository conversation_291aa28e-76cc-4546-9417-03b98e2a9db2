import { MigrationInterface, QueryRunner } from "typeorm";

export class TargetComments1756746377270 implements MigrationInterface {
    name = 'TargetComments1756746377270'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "FK_EMP_PROFILE_ROLE"`);
        await queryRunner.query(`DROP INDEX "employee_profiles_cod_abi_id_index" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`DROP INDEX "nci_wi_employee_profiles_AE9B21BB0FCAE5E9A109B7ACDA988EE2" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`DROP INDEX "nci_wi_employee_profiles_85454FB9523DDA002C8C5CBF50219B17" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`DROP INDEX "nci_wi_employee_profiles_FB43E8513221CBEBAF1FD625FE9D1B63" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`DROP INDEX "nci_wi_employee_e_profiles_606B775558284770BA1813382FE2665C" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`DROP INDEX "ix_employee_profiles_str_supervisory" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`CREATE TABLE "tsc-dev"."target_comments" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_ef985f08ae2eb8dfdfb21d27969" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_a0d970182aeaed787d8a33669c6" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_5e9342b5a8d9fecd13cbe851073" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier, "updatedBy" uniqueidentifier, "targetId" uniqueidentifier NOT NULL, "message" nvarchar(MAX) NOT NULL, "employeeId" uniqueidentifier NOT NULL, CONSTRAINT "PK_ef985f08ae2eb8dfdfb21d27969" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE INDEX "IDX_target_comments_employeeId" ON "tsc-dev"."target_comments" ("employeeId") `);
        await queryRunner.query(`CREATE INDEX "IDX_target_comments_createdAt" ON "tsc-dev"."target_comments" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_target_comments_targetId" ON "tsc-dev"."target_comments" ("targetId") `);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_name"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "bol_deleted"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_supervisory"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_cod_role_id_custom"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "startTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "endTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_hierarchy"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "DF_a5d35afb063dafee7286854df6c"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "bol_ignore_delete"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_function_abi_entity"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" DROP COLUMN "uid_employee_deleted"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" DROP COLUMN "startTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" DROP COLUMN "endTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" DROP COLUMN "int_level"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_name_short"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "uid_employee_updated"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "uid_employee_deleted"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "startTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "endTime"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_sap_code"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_hierarchy"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP CONSTRAINT "DF_788c8b10afb419cd6639b19b554"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "bol_structure_hidden"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "employee_profiles_pk_uid_globalid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "employee_profiles_pk_uid_globalid" PRIMARY KEY ("cod_abi_id")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "uid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "uid" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "employee_profiles_pk_uid_globalid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "employee_profiles_pk_uid_globalid" PRIMARY KEY ("cod_abi_id", "uid")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "cod_profile_id"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "cod_profile_id" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "employee_profiles_pk_uid_globalid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "PK_597712d40ce924b854ed8210360" PRIMARY KEY ("uid")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ALTER COLUMN "str_cod_role_id" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_function"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_function" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" DROP CONSTRAINT "Unq_role_str_name"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ALTER COLUMN "uid_zone" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP CONSTRAINT "tr_zones_PK"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "uid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "uid" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD CONSTRAINT "PK_f25d87d3232e64fcf7cd3f0879d" PRIMARY KEY ("uid")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_name"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_name" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_lang_code"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_lang_code" nvarchar(255) NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_d528697200a122c88909bf5a34" ON "tsc-dev"."employee_profiles" ("cod_abi_id") WHERE "cod_abi_id" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_26dc326b2f518df9896fc410e9" ON "tsc-dev"."employee_profiles" ("str_cod_role_id") WHERE "str_cod_role_id" IS NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "REL_a271ba5f8360b25270b58b8241" ON "tsc-dev"."role" ("uid_zone") WHERE "uid_zone" IS NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "FK_d528697200a122c88909bf5a346" FOREIGN KEY ("cod_abi_id") REFERENCES "tsc-dev"."employees"("int_employeeglobalid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "FK_26dc326b2f518df9896fc410e96" FOREIGN KEY ("str_cod_role_id") REFERENCES "tsc-dev"."role"("cod_role_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ADD CONSTRAINT "FK_a271ba5f8360b25270b58b82414" FOREIGN KEY ("uid_zone") REFERENCES "tsc-dev"."tr_zones"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."target_comments" ADD CONSTRAINT "FK_7bf835cc620e48b2efc09e65be0" FOREIGN KEY ("targetId") REFERENCES "tsc-dev"."targets"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."target_comments" ADD CONSTRAINT "FK_80e6e7cbbb182ba1b6cbb30e1ef" FOREIGN KEY ("employeeId") REFERENCES "tsc-dev"."proposals_employees"("employeeUuid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tsc-dev"."target_comments" DROP CONSTRAINT "FK_80e6e7cbbb182ba1b6cbb30e1ef"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."target_comments" DROP CONSTRAINT "FK_7bf835cc620e48b2efc09e65be0"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" DROP CONSTRAINT "FK_a271ba5f8360b25270b58b82414"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "FK_26dc326b2f518df9896fc410e96"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "FK_d528697200a122c88909bf5a346"`);
        await queryRunner.query(`DROP INDEX "REL_a271ba5f8360b25270b58b8241" ON "tsc-dev"."role"`);
        await queryRunner.query(`DROP INDEX "REL_26dc326b2f518df9896fc410e9" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`DROP INDEX "REL_d528697200a122c88909bf5a34" ON "tsc-dev"."employee_profiles"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_lang_code"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_lang_code" char(5) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "str_name"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_name" nvarchar(60) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP CONSTRAINT "PK_f25d87d3232e64fcf7cd3f0879d"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" DROP COLUMN "uid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "uid" uniqueidentifier NOT NULL CONSTRAINT "DF_f25d87d3232e64fcf7cd3f0879d" DEFAULT NEWSEQUENTIALID()`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD CONSTRAINT "tr_zones_PK" PRIMARY KEY ("uid")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ALTER COLUMN "uid_zone" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ADD CONSTRAINT "Unq_role_str_name" UNIQUE ("str_name")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "str_function"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_function" nvarchar(128)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ALTER COLUMN "str_cod_role_id" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "PK_597712d40ce924b854ed8210360"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "employee_profiles_pk_uid_globalid" PRIMARY KEY ("cod_abi_id", "uid")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "cod_profile_id"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "cod_profile_id" nvarchar(64) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "employee_profiles_pk_uid_globalid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "employee_profiles_pk_uid_globalid" PRIMARY KEY ("cod_abi_id")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP COLUMN "uid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "uid" uniqueidentifier NOT NULL CONSTRAINT "DF_597712d40ce924b854ed8210360" DEFAULT NEWSEQUENTIALID()`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" DROP CONSTRAINT "employee_profiles_pk_uid_globalid"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "employee_profiles_pk_uid_globalid" PRIMARY KEY ("uid", "cod_abi_id")`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "bol_structure_hidden" bit`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD CONSTRAINT "DF_788c8b10afb419cd6639b19b554" DEFAULT 0 FOR "bol_structure_hidden"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_hierarchy" nvarchar(1024)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_sap_code" nvarchar(30)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "endTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "startTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "uid_employee_deleted" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "uid_employee_updated" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."tr_zones" ADD "str_name_short" nvarchar(5) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ADD "int_level" int`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ADD "endTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ADD "startTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."role" ADD "uid_employee_deleted" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_function_abi_entity" nvarchar(128)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "bol_ignore_delete" bit`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "DF_a5d35afb063dafee7286854df6c" DEFAULT 0 FOR "bol_ignore_delete"`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_hierarchy" nvarchar(MAX)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "endTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "startTime" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_cod_role_id_custom" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_supervisory" nvarchar(64)`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "bol_deleted" bit`);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD "str_name" nvarchar(64)`);
        await queryRunner.query(`DROP INDEX "IDX_target_comments_targetId" ON "tsc-dev"."target_comments"`);
        await queryRunner.query(`DROP INDEX "IDX_target_comments_createdAt" ON "tsc-dev"."target_comments"`);
        await queryRunner.query(`DROP INDEX "IDX_target_comments_employeeId" ON "tsc-dev"."target_comments"`);
        await queryRunner.query(`DROP TABLE "tsc-dev"."target_comments"`);
        await queryRunner.query(`CREATE INDEX "ix_employee_profiles_str_supervisory" ON "tsc-dev"."employee_profiles" ("str_supervisory", "str_hierarchy") `);
        await queryRunner.query(`CREATE INDEX "nci_wi_employee_e_profiles_606B775558284770BA1813382FE2665C" ON "tsc-dev"."employee_profiles" ("str_function_abi_entity") `);
        await queryRunner.query(`CREATE INDEX "nci_wi_employee_profiles_FB43E8513221CBEBAF1FD625FE9D1B63" ON "tsc-dev"."employee_profiles" ("cod_abi_id", "cod_profile_id", "str_cod_role_id", "str_name", "str_supervisory") `);
        await queryRunner.query(`CREATE INDEX "nci_wi_employee_profiles_85454FB9523DDA002C8C5CBF50219B17" ON "tsc-dev"."employee_profiles" ("str_cod_role_id") `);
        await queryRunner.query(`CREATE INDEX "nci_wi_employee_profiles_AE9B21BB0FCAE5E9A109B7ACDA988EE2" ON "tsc-dev"."employee_profiles" ("cod_profile_id") `);
        await queryRunner.query(`CREATE INDEX "employee_profiles_cod_abi_id_index" ON "tsc-dev"."employee_profiles" ("cod_abi_id") `);
        await queryRunner.query(`ALTER TABLE "tsc-dev"."employee_profiles" ADD CONSTRAINT "FK_EMP_PROFILE_ROLE" FOREIGN KEY ("str_cod_role_id") REFERENCES "tsc-dev"."role"("cod_role_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
