import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TargetTypeEntity } from '../entities/target-types.entity';
import { BaseRepository } from './base.repository';

@Injectable()
export class TargetTypeRepository extends BaseRepository<TargetTypeEntity> {
  constructor(
    @InjectRepository(TargetTypeEntity)
    public repository: Repository<TargetTypeEntity>
  ) {
    super(repository);
  }

  async createTargetType(targetType: TargetTypeEntity): Promise<TargetTypeEntity> {
    const savedTargetType = await this.repository.save(targetType);
    return savedTargetType;
  }

  async createMultipleTargetTypes(targetTypes: TargetTypeEntity[]): Promise<TargetTypeEntity[]> {
    const savedTargetTypes = await this.repository.save(targetTypes);
    return savedTargetTypes;
  }

  async findTargetTypesByTargetUid(uidTarget: string): Promise<TargetTypeEntity[]> {
    return this.repository.find({
      where: { uidTarget }
    });
  }

  async findTargetTypesByTargetUids(uidTargets: string[]): Promise<TargetTypeEntity[]> {
    if (uidTargets.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('targetType')
      .where('targetType.uidTarget IN (:...uidTargets)', { uidTargets })
      .getMany();
  }

  async updateTargetType(targetType: TargetTypeEntity): Promise<TargetTypeEntity> {
    const updatedTargetType = await this.repository.save(targetType);
    return updatedTargetType;
  }

  async deleteTargetTypesByTargetUid(uidTarget: string): Promise<void> {
    await this.repository.delete({ uidTarget });
  }

  async deleteTargetTypesByTargetUids(uidTargets: string[]): Promise<void> {
    if (uidTargets.length === 0) {
      return;
    }

    await this.repository.createQueryBuilder().delete().where('uidTarget IN (:...uidTargets)', { uidTargets }).execute();
  }
}
